<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
  <circle cx="50" cy="50" r="40" stroke="#e0e0e0" stroke-width="8" fill="none" />
  <circle cx="50" cy="50" r="40" stroke="#3498db" stroke-width="8" fill="none" stroke-dasharray="251.2" stroke-dashoffset="251.2">
    <animateTransform
      attributeName="transform"
      type="rotate"
      from="0 50 50"
      to="360 50 50"
      dur="2s"
      repeatCount="indefinite"
    />
    <animate
      attributeName="stroke-dashoffset"
      values="251.2;0"
      dur="2s"
      repeatCount="indefinite"
    />
  </circle>
</svg>
