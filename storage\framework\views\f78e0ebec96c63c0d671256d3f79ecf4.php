<?php use Illuminate\Support\Facades\Gate; ?>
<aside class="sidebar">
    <!-- Sidebar Header -->
    <div class="sidebar-header">
        <div class="sidebar-logo">
            <?php if(auth()->user()->role == 'admin'): ?>
                <a href="<?php echo e(route('dashboard')); ?>" class="sidebar-logo">
            <?php else: ?>
                <a href="<?php echo e(route('reporting')); ?>" class="sidebar-logo">
            <?php endif; ?>
                <img src="<?php echo e(asset('images/logo.svg')); ?>" alt="Logo">
                <span class="sidebar-logo-text"><?php echo e(config('app.name', 'Reporter')); ?></span>
            </a>
        </div>

        <button class="sidebar-toggle" type="button">
            <i class="fas fa-chevron-left"></i>
        </button>
    </div>

    <!-- Sidebar Navigation -->
    <div class="sidebar-nav">
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('is-admin')): ?>
            <div class="sidebar-nav-item">
                <a href="<?php echo e(route('dashboard')); ?>" class="sidebar-nav-link <?php echo e(request()->routeIs('dashboard') ? 'active' : ''); ?>">
                    <div class="sidebar-nav-icon">
                        <i class="fas fa-tachometer-alt"></i>
                    </div>
                    <span class="sidebar-nav-text"><?php echo e(__('Dashboard')); ?></span>
                </a>
            </div>
        <?php endif; ?>

        <div class="sidebar-nav-item">
            <a href="<?php echo e(route('reporting')); ?>" class="sidebar-nav-link <?php echo e(request()->routeIs('reporting') ? 'active' : ''); ?>">
                <div class="sidebar-nav-icon">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <span class="sidebar-nav-text"><?php echo e(__('Reporting')); ?></span>
            </a>
        </div>

        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('is-admin')): ?>
            <div class="sidebar-nav-item">
                <a href="<?php echo e(route('users')); ?>" class="sidebar-nav-link <?php echo e(request()->routeIs('users') ? 'active' : ''); ?>">
                    <div class="sidebar-nav-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <span class="sidebar-nav-text"><?php echo e(__('Users')); ?></span>
                </a>
            </div>

            <div class="sidebar-nav-item">
                <a href="<?php echo e(route('projects')); ?>" class="sidebar-nav-link <?php echo e(request()->routeIs('projects') ? 'active' : ''); ?>">
                    <div class="sidebar-nav-icon">
                        <i class="fas fa-project-diagram"></i>
                    </div>
                    <span class="sidebar-nav-text"><?php echo e(__('Projects')); ?></span>
                </a>
            </div>
        <?php endif; ?>
    </div>

    <!-- Sidebar User -->
    <div class="sidebar-user">
        <div class="sidebar-user-avatar">
            <?php echo e(substr(Auth::user()->name, 0, 1)); ?>

        </div>
        <div class="sidebar-user-info">
            <h6 class="sidebar-user-name"><?php echo e(Auth::user()->name); ?></h6>
            <p class="sidebar-user-role"><?php echo e(ucfirst(Auth::user()->role ?? 'User')); ?></p>
        </div>
    </div>

    <!-- Sidebar Footer -->
    <div class="sidebar-footer">
        <p>&copy; <?php echo e(date('Y')); ?> <?php echo e(config('app.name')); ?></p>
    </div>
</aside>
<?php /**PATH E:\Web_Development\testerReport\resources\views/tabler/sidebar.blade.php ENDPATH**/ ?>