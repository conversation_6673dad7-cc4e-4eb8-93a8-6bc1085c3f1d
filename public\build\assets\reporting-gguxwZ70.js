document.addEventListener("DOMContentLoaded",function(){let r="",i="";p();function p(){h(),g(),b(),v(),l(),_(),w()}function h(){$("#reportForm").on("submit",function(e){e.preventDefault(),showLoading();let a=new FormData(this);$(".report-form-submit").prop("disabled",!0),fetch(reportStoreRoute,{method:"POST",body:a,headers:{"X-CSRF-TOKEN":csrfToken}}).then(o=>{const n=o.headers.get("content-type");return n&&n.includes("application/json")?o.json():o.text().then(t=>{throw new Error("Invalid JSON: "+t)})}).then(o=>{o.success?($("#report-input-modal").modal("hide"),$("#closeModalbtn").click(),$("#reportForm")[0].reset(),$(".report-form-submit").prop("disabled",!1),hideLoading(),$("#reports-table").DataTable().ajax.reload(),Swal.fire({title:"Success!",text:"Report has been saved successfully.",icon:"success",confirmButtonText:"OK"})):(console.log(o.errors),$(".report-form-submit").prop("disabled",!1),hideLoading(),Swal.fire({title:"Error!",text:"There was an error saving the report.",icon:"error",confirmButtonText:"OK"}))}).catch(o=>{console.error("Error: ",o),hideLoading(),$(".report-form-submit").prop("disabled",!1),Swal.fire({title:"Error!",text:"There was an error processing your request.",icon:"error",confirmButtonText:"OK"})})})}function g(){$(document).on("click",".deleteReport",function(e){e.preventDefault();let o="/report/"+$(this).data("id");Swal.fire({title:"Want to Delete?",text:"This will delete the selected Report!",icon:"warning",showCancelButton:!0,confirmButtonText:"Yes, delete it!",cancelButtonText:"No, cancel!",reverseButtons:!0,customClass:{confirmButton:"btn btn-danger ml-3",cancelButton:"btn btn-secondary mr-2"},buttonsStyling:!1}).then(n=>{n.isConfirmed?$.ajax({url:o,type:"DELETE",data:{_token:csrfToken},beforeSend:function(){showLoading()},success:function(t){$("#reports-table").DataTable().ajax.reload(),hideLoading(),Swal.fire({title:"Deleted!",text:"Report has been deleted successfully.",icon:"success",confirmButtonText:"OK"})},error:function(t){hideLoading(),Swal.fire({title:"Error!",text:"There was an issue deleting the report.",icon:"error",confirmButtonText:"OK"})}}):n.dismiss===Swal.DismissReason.cancel&&Swal.fire({title:"Cancelled",text:"Your report is safe :)",icon:"info",confirmButtonText:"OK"})})})}function b(){$(document).on("click",".editReport",function(e){e.preventDefault();let a=$(this).data("id");var o=userRoleGlobal;$.ajaxSetup({headers:{"X-CSRF-TOKEN":$('meta[name="csrf-token"]').attr("content")}}),$.ajax({url:reportUpdateRoute,method:"patch",data:{id:a},beforeSend:function(){showLoading()},success:function(n){hideLoading();var t=n[1];$("#report-input-modal").modal("show"),$("#id").val(t.id),o!="user"&&$("#user_id").val(t.user_id),$("#project_id").val(t.project_id),$("#task_tested").val(t.task_tested),$("#bug_reported").val(t.bug_reported),$("#other").val(t.other),$("#description").val(t.description),$("#regression").prop("checked",t.regression),$("#smoke_testing").prop("checked",t.smoke_testing),$("#client_meeting").prop("checked",t.client_meeting),$("#daily_meeting").prop("checked",t.daily_meeting),$("#mobile_testing").prop("checked",t.mobile_testing),$("#automation").prop("checked",t.automation)},error:function(n,t){console.log("Error "+t),hideLoading(),Swal.fire({title:"Error!",text:"There was an error loading the report data.",icon:"error",confirmButtonText:"OK"})}})})}function v(){$("#task_tested, #bug_reported").on("input",function(){let e=$(this).val();e<0?$(this).val(""):$(this).val(Math.floor(e))})}function l(){$.fn.DataTable.isDataTable("#reports-table")&&$("#reports-table").DataTable().clear().destroy(),$("#reports-table").DataTable({serverSide:!0,dom:'<"top" f> rtlp',scrollX:!0,scrollY:"60vh",scrollCollapse:!0,paging:!0,ajax:{url:reportsDataRoute,data:function(e){e.from_date=r,e.to_date=i,e.user_id=$("#user-name").val(),e.project_id=$("#project-name").val()},beforeSend:function(){showLoading(!0)},complete:function(){hideLoading()}},columns:[{data:"date",name:"date"},{data:"user_name",name:"user_name",orderable:!1,searchable:!0},{data:"project_name",name:"project_name",orderable:!1,searchable:!0},{data:"task_tested",name:"task_tested"},{data:"bug_reported",name:"bug_reported"},{data:"regression",name:"regression"},{data:"smoke_testing",name:"smoke_testing"},{data:"client_meeting",name:"client_meeting"},{data:"daily_meeting",name:"daily_meeting"},{data:"mobile_testing",name:"mobile_testing"},{data:"automation",name:"automation"},{data:"other",name:"other"},{data:"description",name:"description"},{data:"action",name:"action"}],columnDefs:[{targets:[0,12],createdCell:function(e,a,o,n,t){$(e).addClass("column-width-height")}}],drawCallback:function(){var e=this.api();function a(m){var f=0;if(e.column(m).data().length>0)return e.column(m).data().each(function(T){var D=parseFloat(T)||0;f+=D}),f}const o=a(3),n=a(4),t=a(5),s=a(6),d=a(7),c=a(8),u=a(9);$(e.column(3).footer()).html(o!==void 0?o:"0"),$(e.column(4).footer()).html(n!==void 0?n:"0"),$(e.column(5).footer()).html(t!==void 0?t:"0"),$(e.column(6).footer()).html(s!==void 0?s:"0"),$(e.column(7).footer()).html(d!==void 0?d:"0"),$(e.column(8).footer()).html(c!==void 0?c:"0"),$(e.column(9).footer()).html(u!==void 0?u:"0")},initComplete:function(e,a){let o=this.api();$(o.columns().footer()).each(function(n){$(this).css("width",$(o.column(n).header()).width())})}})}function _(){$("#date-filter").on("change",function(){if($(this).val()==null||$(this).val()==="")return;$("#user-name").val(""),$("#project-name").val(""),$("#from-date").val(""),$("#to-date").val("");const e=new Date,a=e.toISOString().split("T")[0];e.setDate(e.getDate()-1);const o=e.toISOString().split("T")[0];r=$(this).val(),o===r?i=o:i=a,l()}),$("#date-search-btn").click(function(){$("#date-filter").val(""),r=$("#from-date").val(),i=$("#to-date").val(),l()}),$("#date-reset-btn").click(function(){$("#user-name").val(""),$("#project-name").val(""),$("#date-filter").val(""),$("#from-date").val(""),$("#to-date").val(""),r=i="",l()})}function w(){$("#report-input-modal").on("hidden.bs.modal",function(){$("#reportForm")[0].reset(),$(".report-form-submit").removeClass("disabled")})}});
