/* Reporting Page Specific Styles */

/* Column width and height control */
.column-width-height {
    text-wrap: nowrap;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: all 0.3s ease;
}

.column-width-height:hover {
    text-wrap: wrap;
    max-width: 300px;
    background-color: #f8f9fa;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    z-index: 10;
    position: relative;
}

/* DataTable filter styling */
#reports-table_filter {
    margin: 0.5rem;
}

#reports-table_filter label {
    margin-top: -0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

#reports-table_filter input {
    border-radius: var(--border-radius);
    padding: 0.375rem 0.75rem;
    border: 1px solid var(--border-color);
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

#reports-table_filter input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    outline: 0;
}

/* Date filters styling */
.table-container .dates input {
    border-radius: var(--border-radius);
    padding: 0.375rem 0.75rem;
}

.table-container .dates {
    margin-bottom: -2.5rem;
    margin-top: 0.5rem;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

/* DataTables wrapper styling */
.dataTables_wrapper {
    margin: 0;
    width: 100%;
}

/* Length control styling */
#reports-table_length {
    margin-top: 0.75rem;
}

#reports-table_length select {
    border-radius: 0.75rem;
    padding: 0.375rem 2rem 0.375rem 0.75rem;
    border: 1px solid var(--border-color);
    background-position: right 0.75rem center;
    background-size: 16px 12px;
    appearance: none;
}

/* Pagination styling */
#reports-table_paginate .paginate_button {
    padding: 0.375rem 0.75rem;
    margin: 0 0.25rem;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

#reports-table_paginate .paginate_button.current {
    border-radius: 0.5rem;
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: white !important;
    margin-top: 0.5rem;
}

#reports-table_paginate .paginate_button:hover:not(.current) {
    background-color: #e9ecef !important;
    border-color: #dee2e6 !important;
    color: var(--secondary-color) !important;
}

/* Form controls styling */
input, .form-control, textarea {
    border-radius: var(--border-radius) !important;
    margin-top: 0.25rem;
    padding: 0.375rem 0.75rem;
    border: 1px solid var(--border-color);
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

input:focus, .form-control:focus, textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    outline: 0;
}

/* Modal footer styling */
.modal-footer {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    padding: 1rem;
    border-top: 1px solid var(--border-color);
}

/* Button styling */
.btn {
    border-radius: var(--border-radius);
    padding: 0.375rem 0.75rem;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #2980b9;
    border-color: #2980b9;
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .table-container .dates {
        flex-direction: column;
        margin-bottom: 1rem;
    }

    .column-width-height:hover {
        max-width: 250px;
    }

    #reports-table_filter, #reports-table_length {
        text-align: left;
        margin-left: 0;
    }
}

/* DataTable Single Table Structure - Fix Alignment Issues
#reports-table {
    width: 100% !important;
    margin: 0 !important;
    table-layout: fixed !important;
    border-collapse: separate !important;
    border-spacing: 0 !important;
}

#reports-table th,
#reports-table td {
    box-sizing: border-box !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 8px 12px !important;
    border: 1px solid var(--border-color) !important;
}

.dataTables_wrapper {
    overflow-x: auto;
    width: 100%;
    margin: 0 !important;
    padding: 0 !important;
    position: relative;
}

/* Prevent any margin/padding issues that cause left shift */
.dataTables_wrapper * {
    box-sizing: border-box;
}

.dataTables_scrollHead,
.dataTables_scrollBody,
.dataTables_scrollFoot {
    width: 100% !important;
    overflow: visible !important;
}

.dataTables_scrollHead table,
.dataTables_scrollBody table,
.dataTables_scrollFoot table {
    width: 100% !important;
    table-layout: fixed !important;
    border-collapse: separate !important;
    border-spacing: 0 !important;
}

.dataTables_scrollHeadInner,
.dataTables_scrollFootInner {
    width: 100% !important;
    box-sizing: border-box !important;
}

.dataTables_scrollHead th,
.dataTables_scrollFoot th {
    box-sizing: border-box !important;
    border: 1px solid var(--border-color) !important;
}

.table-responsive {
    overflow: visible !important;
    width: 100%;
}

.px-4.py-3 {
    overflow-x: auto;
    width: 100%;
}

.dataTables_wrapper .dataTables_scroll {
    overflow: auto;
}

.dataTables_wrapper .dataTables_scroll div.dataTables_scrollBody {
    overflow: auto;
    -webkit-overflow-scrolling: touch;
}

.dataTables_scrollHead .dataTables_scrollHeadInner table {
    margin-bottom: 0 !important;
}

.dataTables_scrollFoot .dataTables_scrollFootInner table {
    margin-top: 0 !important;
}

/* Prevent left shift of DataTable body */
.dataTables_scrollHead,
.dataTables_scrollBody,
.dataTables_scrollFoot {
    margin-left: 0 !important;
    padding-left: 0 !important;
    position: relative !important;
}

.dataTables_scrollHeadInner,
.dataTables_scrollFootInner {
    margin-left: 0 !important;
    padding-left: 0 !important;
    box-sizing: border-box !important;
}

/* Ensure all tables in scroll containers are aligned */
.dataTables_scrollHead table,
.dataTables_scrollBody table,
.dataTables_scrollFoot table {
    margin-left: 0 !important;
    margin-right: 0 !important;
    position: relative !important;
    left: 0 !important;
}

/* Force consistent container widths */
.dataTables_wrapper .dataTables_scroll {
    margin: 0 !important;
    padding: 0 !important;
}

/* Additional fix for body alignment */
.dataTables_scrollBody {
    border-left: 0 !important;
    border-right: 0 !important;
}

/* Critical fix for left shift - ensure all containers start at position 0 */
.dataTables_scroll {
    position: relative !important;
    left: 0 !important;
    right: 0 !important;
}

.dataTables_scrollHead,
.dataTables_scrollBody,
.dataTables_scrollFoot {
    transform: translateX(0) !important;
}

/* Ensure table wrapper doesn't have any offset */
#reports-table_wrapper {
    position: relative !important;
    left: 0 !important;
    margin-left: 0 !important;
    padding-left: 0 !important;
}

