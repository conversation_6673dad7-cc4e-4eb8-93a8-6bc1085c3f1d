<?php

namespace App\Providers;

// use Illuminate\Support\Facades\Gate;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        //
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->userRoleGates();
    }

    private function userRoleGates(){
        $this->registerPolicies();
        Gate::define('is-admin',function ($user){
            return $user->role === 'admin';
        });
        Gate::define('is-manager',function ($user){
            return $user->role === 'manager';
        });
        Gate::define('is-user',function ($user){
            return $user->role === 'user';
        });
    }
}
