<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Gate;

class IsAdmin
{
    /**
     * This is the middleware used for Control the traffic according to the request
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (Gate::allows('is-admin')) {
            return $next($request);
        }

        // Optionally, redirect non-admin users or return a 403 response
        return redirect('/')->with('error', 'You do not have admin access.');
    }
}
