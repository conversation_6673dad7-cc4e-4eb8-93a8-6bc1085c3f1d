
/* Common Form Styles */
:root {
  --primary-color: #3498db;
  --secondary-color: #2c3e50;
  --light-bg-color: #f8f9fa;
  --border-color: rgba(187, 178, 178, 0.5);
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  --border-radius: 0.5rem;
  --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --transition-speed: 0.3s;
}

.form-group {
  align-items: center;
  justify-content: end;
  display: flex;
  flex-flow: column;
  padding: 0.5rem;
  flex: 0 0 auto;
  width: 50%;
  margin-bottom: 0.5rem !important;
  transition: all var(--transition-speed) ease;
}

.form-group-checkbox {
  align-items: center;
  justify-content: end;
  display: flex;
  flex-flow: column;
  padding: 0.5rem;
  width: 50%;
  margin-bottom: 0.5rem !important;
  transition: all var(--transition-speed) ease;
}

label {
  font-weight: 600;
  text-align: center;
  margin-bottom: 0.25rem;
}

/* DataTable Styling - Updated for single table structure */
table.dataTable {
  border-collapse: separate !important;
  border-spacing: 0 !important;
  width: 100% !important;
  margin-top: 1rem;
  box-shadow: var(--box-shadow);
  table-layout: fixed !important;
}

table.dataTable tbody tr {
  background-color: whitesmoke;
  transition: background-color 0.2s ease;
}

table.dataTable tbody tr:hover {
  background-color: #e9ecef;
}

#reports-table thead tr {
  height: auto !important;
}

table.dataTable thead th,
table.dataTable thead td {
  padding: 8px 12px !important;
  border: 1px solid var(--border-color) !important;
  background: aliceblue;
  font-weight: bold;
  font-size: small;
  position: relative;
  box-sizing: border-box !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

table.dataTable tfoot th,
table.dataTable tfoot td {
  padding: 8px 12px !important;
  border: 1px solid var(--border-color) !important;
  background: aliceblue;
  font-weight: bold;
  font-size: small;
  box-sizing: border-box !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .form-group, .form-group-checkbox {
    width: 100%;
  }

  table.dataTable thead th,
  table.dataTable thead td,
  table.dataTable tfoot th,
  table.dataTable tfoot td {
    padding: 6px 8px !important;
    font-size: x-small;
    box-sizing: border-box !important;
  }

  /* Ensure mobile scrolling works properly */
  .dataTables_wrapper {
    overflow-x: auto !important;
  }
}
