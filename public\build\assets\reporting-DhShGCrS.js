document.addEventListener("DOMContentLoaded",function(){let n="",i="";m();function m(){h(),b(),g(),w(),l(),v(),x()}function h(){$("#reportForm").on("submit",function(e){e.preventDefault(),showLoading();let o=new FormData(this);$(".report-form-submit").prop("disabled",!0),fetch(reportStoreRoute,{method:"POST",body:o,headers:{"X-CSRF-TOKEN":csrfToken}}).then(a=>{const r=a.headers.get("content-type");return r&&r.includes("application/json")?a.json():a.text().then(t=>{throw new Error("Invalid JSON: "+t)})}).then(a=>{a.success?($("#report-input-modal").modal("hide"),$("#closeModalbtn").click(),$("#reportForm")[0].reset(),$(".report-form-submit").prop("disabled",!1),hideLoading(),$("#reports-table").DataTable().ajax.reload(),Swal.fire({title:"Success!",text:"Report has been saved successfully.",icon:"success",confirmButtonText:"OK"})):(console.log(a.errors),$(".report-form-submit").prop("disabled",!1),hideLoading(),Swal.fire({title:"Error!",text:"There was an error saving the report.",icon:"error",confirmButtonText:"OK"}))}).catch(a=>{console.error("Error: ",a),hideLoading(),$(".report-form-submit").prop("disabled",!1),Swal.fire({title:"Error!",text:"There was an error processing your request.",icon:"error",confirmButtonText:"OK"})})})}function b(){$(document).on("click",".deleteReport",function(e){e.preventDefault();let a="/report/"+$(this).data("id");Swal.fire({title:"Want to Delete?",text:"This will delete the selected Report!",icon:"warning",showCancelButton:!0,confirmButtonText:"Yes, delete it!",cancelButtonText:"No, cancel!",reverseButtons:!0,customClass:{confirmButton:"btn btn-danger ml-3",cancelButton:"btn btn-secondary mr-2"},buttonsStyling:!1}).then(r=>{r.isConfirmed?$.ajax({url:a,type:"DELETE",data:{_token:csrfToken},beforeSend:function(){showLoading()},success:function(t){$("#reports-table").DataTable().ajax.reload(),hideLoading(),Swal.fire({title:"Deleted!",text:"Report has been deleted successfully.",icon:"success",confirmButtonText:"OK"})},error:function(t){hideLoading(),Swal.fire({title:"Error!",text:"There was an issue deleting the report.",icon:"error",confirmButtonText:"OK"})}}):r.dismiss===Swal.DismissReason.cancel&&Swal.fire({title:"Cancelled",text:"Your report is safe :)",icon:"info",confirmButtonText:"OK"})})})}function g(){$(document).on("click",".editReport",function(e){e.preventDefault();let o=$(this).data("id");var a=userRoleGlobal;$.ajaxSetup({headers:{"X-CSRF-TOKEN":$('meta[name="csrf-token"]').attr("content")}}),$.ajax({url:reportUpdateRoute,method:"patch",data:{id:o},beforeSend:function(){showLoading()},success:function(r){hideLoading();var t=r[1];$("#report-input-modal").modal("show"),$("#id").val(t.id),a!="user"&&$("#user_id").val(t.user_id),$("#project_id").val(t.project_id),$("#task_tested").val(t.task_tested),$("#bug_reported").val(t.bug_reported),$("#other").val(t.other),$("#description").val(t.description),$("#regression").prop("checked",t.regression),$("#smoke_testing").prop("checked",t.smoke_testing),$("#client_meeting").prop("checked",t.client_meeting),$("#daily_meeting").prop("checked",t.daily_meeting),$("#mobile_testing").prop("checked",t.mobile_testing),$("#automation").prop("checked",t.automation)},error:function(r,t){console.log("Error "+t),hideLoading(),Swal.fire({title:"Error!",text:"There was an error loading the report data.",icon:"error",confirmButtonText:"OK"})}})})}function w(){$("#task_tested, #bug_reported").on("input",function(){let e=$(this).val();e<0?$(this).val(""):$(this).val(Math.floor(e))})}function l(){$.fn.DataTable.isDataTable("#reports-table")&&$("#reports-table").DataTable().clear().destroy(),$("#reports-table").DataTable({serverSide:!0,dom:'<"top" f> rtlp',responsive:!1,paging:!0,scrollX:!0,scrollY:"400px",scrollCollapse:!0,autoWidth:!1,fixedColumns:!1,ajax:{url:reportsDataRoute,data:function(e){e.from_date=n,e.to_date=i,e.user_id=$("#user-name").val(),e.project_id=$("#project-name").val()},beforeSend:function(){showLoading(!0)},complete:function(){hideLoading()}},columns:[{data:"date",name:"date",width:"100px"},{data:"user_name",name:"user_name",searchable:!0,width:"120px"},{data:"project_name",name:"project_name",searchable:!0,width:"120px"},{data:"task_tested",name:"task_tested",width:"80px"},{data:"bug_reported",name:"bug_reported",orderable:!1,searchable:!1,width:"80px"},{data:"regression",name:"regression",orderable:!1,searchable:!1,width:"80px"},{data:"smoke_testing",name:"smoke_testing",orderable:!1,searchable:!1,width:"80px"},{data:"client_meeting",name:"client_meeting",orderable:!1,searchable:!1,width:"80px"},{data:"daily_meeting",name:"daily_meeting",orderable:!1,searchable:!1,width:"80px"},{data:"mobile_testing",name:"mobile_testing",orderable:!1,searchable:!1,width:"80px"},{data:"automation",name:"automation",orderable:!1,searchable:!1,width:"80px"},{data:"other",name:"other",orderable:!1,searchable:!1,width:"80px"},{data:"description",name:"description",width:"200px"},{data:"action",name:"action",orderable:!1,searchable:!1,width:"100px"}],columnDefs:[{targets:"_all",className:"dt-head-center dt-body-center",createdCell:function(e){$(e).css({"text-align":"center","vertical-align":"middle","white-space":"nowrap",overflow:"hidden","text-overflow":"ellipsis"})}}],drawCallback:function(){var e=this.api();function o(f){var p=0;if(e.column(f).data().length>0)return e.column(f).data().each(function(_){var T=parseFloat(_)||0;p+=T}),p}const a=o(3),r=o(4),t=o(5),s=o(6),d=o(7),c=o(8),u=o(9);$(e.column(3).footer()).html(a!==void 0?a:"0"),$(e.column(4).footer()).html(r!==void 0?r:"0"),$(e.column(5).footer()).html(t!==void 0?t:"0"),$(e.column(6).footer()).html(s!==void 0?s:"0"),$(e.column(7).footer()).html(d!==void 0?d:"0"),$(e.column(8).footer()).html(c!==void 0?c:"0"),$(e.column(9).footer()).html(u!==void 0?u:"0")},initComplete:function(){var e=this.api();function o(){e.columns.adjust();var a=$("#reports-table").outerWidth();$(".dataTables_scrollHead, .dataTables_scrollFoot").css({width:"100%",margin:"0",padding:"0",position:"relative",left:"0",transform:"translateX(0)"}),$(".dataTables_scrollHeadInner, .dataTables_scrollFootInner").css({width:a+"px",margin:"0",padding:"0","padding-right":"0","padding-left":"0","box-sizing":"border-box"}),$(".dataTables_scrollFootInner, .dataTables_scrollHeadInner").each(function(){this.style.setProperty("padding-right","0","important"),this.style.setProperty("padding-left","0","important"),this.style.setProperty("padding","0","important")}),$(".dataTables_scrollHead table, table, .dataTables_scrollFoot table").css({width:a+"px","table-layout":"fixed",margin:"0",position:"relative",left:"0"})}setTimeout(o,100),window.fixDataTableAlignment=o,$(window).on("resize.dataTableFix",function(){clearTimeout(window.resizeTimer),window.resizeTimer=setTimeout(function(){$.fn.DataTable.isDataTable("#reports-table")&&o()},100)})}})}function v(){$("#date-filter").on("change",function(){if($(this).val()==null||$(this).val()==="")return;$("#user-name").val(""),$("#project-name").val(""),$("#from-date").val(""),$("#to-date").val("");const e=new Date,o=e.toISOString().split("T")[0];e.setDate(e.getDate()-1);const a=e.toISOString().split("T")[0];n=$(this).val(),a===n?i=a:i=o,l()}),$("#date-search-btn").click(function(){$("#date-filter").val(""),n=$("#from-date").val(),i=$("#to-date").val(),l()}),$("#date-reset-btn").click(function(){$("#user-name").val(""),$("#project-name").val(""),$("#date-filter").val(""),$("#from-date").val(""),$("#to-date").val(""),n=i="",l()})}function x(){$("#report-input-modal").on("hidden.bs.modal",function(){$("#reportForm")[0].reset(),$(".report-form-submit").removeClass("disabled")})}});
