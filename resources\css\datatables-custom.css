/**
 * Tester Report - DataTables Custom Styling
 * Modern styling for DataTables with responsive design
 */

/* DataTable Container */
.dataTables_wrapper {
  margin-bottom: var(--spacing-lg);
  font-size: 0.875rem;
  width: 100%;
  padding: 0;
}

/* DataTable Table */
table.dataTable {
  width: 100% !important;
  margin: 0 !important;
  border-collapse: separate !important;
  border-spacing: 0;
  border-radius: 0;
  overflow: hidden;
  box-shadow: none;
}

/* DataTable Header */
table.dataTable thead th,
table.dataTable thead td {
  padding: var(--spacing-md) !important;
  border-bottom: 1px solid var(--neutral-200) !important;
  background-color: var(--neutral-50);
  font-weight: 600;
  color: var(--neutral-700);
  white-space: nowrap;
  position: relative;
}

table.dataTable thead th:first-child {
  border-left: 1px solid var(--neutral-200);
}

table.dataTable thead th:last-child {
  border-right: 1px solid var(--neutral-200);
}

/* DataTable Sorting Icons */
table.dataTable thead .sorting,
table.dataTable thead .sorting_asc,
table.dataTable thead .sorting_desc {
  cursor: pointer;
  position: relative;
}

table.dataTable thead .sorting:after,
table.dataTable thead .sorting_asc:after,
table.dataTable thead .sorting_desc:after {
  position: absolute;
  right: 8px;
  font-family: "Font Awesome 5 Free";
  opacity: 0.5;
}

table.dataTable thead .sorting:after {
  content: "\f0dc";
  opacity: 0.2;
}

table.dataTable thead .sorting_asc:after {
  content: "\f0de";
  opacity: 1;
}

table.dataTable thead .sorting_desc:after {
  content: "\f0dd";
  opacity: 1;
}

/* DataTable Body */
table.dataTable tbody tr {
  background-color: white;
  transition: background-color var(--transition-fast) ease;
}

table.dataTable tbody tr:hover {
  background-color: var(--neutral-100);
}

table.dataTable tbody tr.selected {
  background-color: rgba(67, 97, 238, 0.1);
}

table.dataTable tbody td {
  padding: var(--spacing-md) !important;
  border-bottom: 1px solid var(--neutral-200);
  border-left: 1px solid var(--neutral-200);
  border-right: 1px solid var(--neutral-200);
  vertical-align: middle;
}

/* DataTable Footer */
table.dataTable tfoot th,
table.dataTable tfoot td {
  padding: var(--spacing-md) !important;
  border-top: 1px solid var(--neutral-200);
  background-color: var(--neutral-50);
  font-weight: 600;
  color: var(--neutral-700);
}

table.dataTable tfoot th:first-child {
  border-left: 1px solid var(--neutral-200);
}

table.dataTable tfoot th:last-child {
  border-right: 1px solid var(--neutral-200);
}

/* DataTable Pagination */
.dataTables_paginate {
  margin: var(--spacing-md) var(--spacing-md) !important;
  display: flex;
  justify-content: flex-end;
}

.dataTables_paginate .paginate_button {
  padding: 0.375rem 0.75rem !important;
  margin: 0 0.125rem;
  border-radius: var(--border-radius-md) !important;
  border: 1px solid var(--neutral-300) !important;
  background-color: white !important;
  color: var(--neutral-700) !important;
  cursor: pointer;
  transition: all var(--transition-fast) ease !important;
}

.dataTables_paginate .paginate_button:hover {
  background-color: var(--neutral-100) !important;
  color: var(--primary-color) !important;
  border-color: var(--primary-light) !important;
}

.dataTables_paginate .paginate_button.current,
.dataTables_paginate .paginate_button.current:hover {
  background-color: var(--primary-color) !important;
  color: white !important;
  border-color: var(--primary-color) !important;
  font-weight: 600;
}

.dataTables_paginate .paginate_button.disabled,
.dataTables_paginate .paginate_button.disabled:hover {
  color: var(--neutral-400) !important;
  background-color: var(--neutral-100) !important;
  border-color: var(--neutral-200) !important;
  cursor: not-allowed;
  opacity: 0.6;
}

/* DataTable Length */
.dataTables_length {
  margin: var(--spacing-md) var(--spacing-md) var(--spacing-md) var(--spacing-md);
}

.dataTables_length select {
  padding: 0.375rem 1.75rem 0.375rem 0.75rem !important;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: var(--neutral-800);
  background-color: white;
  border: 1px solid var(--neutral-300);
  border-radius: var(--border-radius-md);
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 16px 12px;
}

.dataTables_length select:focus {
  border-color: var(--primary-light);
  outline: 0;
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.25);
}

/* DataTable Filter */
.dataTables_filter {
  margin: var(--spacing-md) var(--spacing-md) var(--spacing-md) var(--spacing-md);
  display: flex;
  justify-content: flex-end;
}

.dataTables_filter input {
  padding: 0.375rem 0.75rem !important;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: var(--neutral-800);
  background-color: white;
  border: 1px solid var(--neutral-300);
  border-radius: var(--border-radius-md);
  transition: border-color var(--transition-fast) ease-in-out, box-shadow var(--transition-fast) ease-in-out;
}

.dataTables_filter input:focus {
  border-color: var(--primary-light);
  outline: 0;
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.25);
}

/* DataTable Info */
.dataTables_info {
  margin: var(--spacing-md) var(--spacing-md);
  color: var(--neutral-600);
  font-size: 0.875rem;
}

/* DataTable Processing */
.dataTables_processing {
  background-color: rgba(255, 255, 255, 0.9) !important;
  color: var(--primary-color) !important;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-md) var(--spacing-lg) !important;
  font-weight: 600;
  z-index: 10;
}

/* DataTable Empty */
.dataTables_empty {
  padding: var(--spacing-xl) !important;
  text-align: center;
  color: var(--neutral-500);
  background-color: var(--neutral-50) !important;
}

/* DataTable Responsive */
@media (max-width: 768px) {
  .dataTables_wrapper {
    overflow-x: auto;
  }

  .dataTables_length,
  .dataTables_filter,
  .dataTables_info,
  .dataTables_paginate {
    width: 100%;
    text-align: left;
    justify-content: flex-start;
  }

  .dataTables_filter {
    margin-top: var(--spacing-sm);
  }

  .dataTables_paginate {
    margin-top: var(--spacing-sm) !important;
    overflow-x: auto;
  }
}
