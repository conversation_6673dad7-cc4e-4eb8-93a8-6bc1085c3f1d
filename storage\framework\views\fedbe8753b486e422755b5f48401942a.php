<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AppLayout::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/reporting.css']); ?>
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div class="flex-grow-1 text-center">
                <div class=" h2 card-title">
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('is-admin')): ?>
                        <?php echo e(__("All QA's Reports")); ?>

                    <?php endif; ?>
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('is-user')): ?>
                        <?php echo e(__('Today Report')); ?>

                    <?php endif; ?>
                </div>
            </div>
            <div>
                <button type="button" class="btn btn-primary addTask" data-bs-toggle="modal"
                    data-bs-target="#report-input-modal">
                    <i class="fas fa-plus me-2"></i> Add Task
                </button>
            </div>
        </div>
        <div class="card-body p-0">
                <div class="table-responsive">
                    <div class="d-flex flex-column flex-md-row justify-content-between mb-3">
                        <div class="order-2 order-md-1">
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('is-admin')): ?>
                                <div class="dates d-flex gap-2">
                                    <div class="dates-container row g-2">
                                        <div class="col-12 col-sm-6 col-md-4 col-lg-2">
                                            <label for="date-filter">Date</label>
                                            <select class="form-select rounded-3" id="date-filter">
                                                <option value="" selected disabled>Select</option>
                                                <option value="<?php echo e($dateOptions['today']); ?>">Today </option>
                                                <option value="<?php echo e($dateOptions['yesterday']); ?>">Yesterday</option>
                                                <option value="<?php echo e($dateOptions['last3Days']); ?>">Last 3 days</option>
                                                <option value="<?php echo e($dateOptions['last7Days']); ?>">Last 7 days</option>
                                                <option value="<?php echo e($dateOptions['last15Days']); ?>">Last 15 days </option>
                                                <option value="<?php echo e($dateOptions['last30Days']); ?>">Last 30 days </option>
                                            </select>
                                        </div>
                                        <div class="col-12 col-sm-6 col-md-4 col-lg-2">
                                            <label for="user-name">Users</label>
                                            <select class="form-select rounded-3" id="user-name" name="user-name">
                                                <option value="">Choose</option>
                                                <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($user->id); ?>"><?php echo e($user->name); ?></option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>
                                        <div class="col-12 col-sm-6 col-md-4 col-lg-2">
                                            <label for="project-name">Projects</label>
                                            <select class="form-select rounded-3" id="project-name" name="project-name">
                                                <option value="">Choose</option>
                                                <?php $__currentLoopData = $allprojects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($project->id); ?>"><?php echo e($project->name); ?></option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>
                                        <div class="col-12 col-sm-6 col-md-6 col-lg-4">
                                            <div class="row g-2">
                                                <div class="col-6">
                                                    <label for="from-date">Date From:</label>
                                                    <input type="date" id="from-date" name="from-date"
                                                        class="form-control rounded-3">
                                                </div>
                                                <div class="col-6">
                                                    <label for="to-date">Date To:</label>
                                                    <input type="date" id="to-date" name="to-date"
                                                        class="form-control rounded-3">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-12 col-sm-12 col-md-6 col-lg-2 d-flex align-items-end gap-2">
                                            <button type="button" class="btn btn-success rounded-3 flex-grow-1"
                                                id='date-search-btn'>Search</button>
                                            <button type="button" class="btn btn-primary rounded-3 flex-grow-1"
                                                id='date-reset-btn'>Reset</button>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="px-4 py-3">
                        <h2 class="text-center h3 " style="margin-bottom: -2rem">Latest Reports Record</h2>
                        <table id="reports-table" class="table table-striped table-bordered" style="width:100%">
                        <thead>
                            <tr class="text-center">
                                <th> Date</th>
                                <th> Name</th>
                                <th> Project</th>
                                <th> Tasks<br>Tested</th>
                                <th> Bugs<br>Reported</th>
                                <th> Regression<br>Testing</th>
                                <th> Smoke<br>Testing</th>
                                <th> Client<br> Meeting</th>
                                <th> Daily <br>Meeting</th>
                                <th> Mobile <br>Testing</th>
                                <th> Automation <br>Testing</th>
                                <th> Other</th>
                                <th> Description</th>
                                <th> Action</th>
                            </tr>
                        </thead>
                        <tbody class="text-center">

                        </tbody>
                        <tfoot>
                            <tr class="text-center">
                                <th></th>
                                <th> </th>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th> </th>
                                <th> </th>
                                <th> </th>
                                <th> </th>
                            </tr>
                        </tfoot>
                    </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Modal -->
    <?php echo $__env->make('modals.add-tester-report', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<script>
    // Define global variables for use in the reporting.js file
    const reportStoreRoute = "<?php echo e(route('user-reports.store')); ?>";
    const reportUpdateRoute = "<?php echo e(route('user-reports.update')); ?>";
    const reportsDataRoute = "<?php echo e(route('reports.data')); ?>";
    const csrfToken = "<?php echo e(csrf_token()); ?>";
    const userRoleGlobal = "<?php echo e(auth()->user()->role); ?>";
</script>
<?php echo app('Illuminate\Foundation\Vite')(['resources/js/reporting.js']); ?>
<?php /**PATH E:\Web_Development\testerReport\resources\views/qareport/reporting.blade.php ENDPATH**/ ?>