<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <meta name="description" content="QA Reporting System for tracking testing activities">
    <meta name="author" content="Tester Report Team">

    <title><?php echo e(config('app.name', 'Reporter')); ?></title>

    <!-- Favicon -->
    <link rel="icon" href="<?php echo e(asset('favicon.ico')); ?>" type="image/x-icon">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Core CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('css/bootstrap.min.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('css/jquery.dataTables.min.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('css/sweetalert2.min.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('css/multi-select-tag.css')); ?>">

    <!-- Theme CSS -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/datatables-custom.css', 'resources/css/sidebar.css', 'resources/css/navbar.css', 'resources/css/footer.css', 'resources/css/theme.css']); ?>
    <!-- App CSS -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js', 'resources/css/style.css']); ?>

    <!-- Additional CSS -->
    <?php echo $__env->yieldPushContent('styles'); ?>
</head>

<body class="font-sans antialiased">
    <!-- Sidebar Backdrop (Mobile) -->
    <div class="sidebar-backdrop"></div>

    <div class="page">
        <!-- Sidebar -->
        <?php echo $__env->make('tabler.sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <div class="page-wrapper">
            <!-- Navbar -->
            <?php echo $__env->make('layouts.navigation', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

            <!-- Page Heading -->
            <?php if(isset($header)): ?>
            <header class="bg-white shadow">
                <div class="container-fluid py-3 px-4">
                    <?php echo e($header); ?>

                </div>
            </header>
            <?php endif; ?>

            <!-- Page Content -->
            <main>
                <div class="container-fluid py-3 px-4">
                    <?php echo e($slot); ?>

                </div>
                <?php echo $__env->make('modals.loading', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            </main>

            <!-- Footer -->
            <footer class="footer">
                <div class="container-fluid py-3 px-4">
                    <div class="row">
                        <div class="col-md-6">
                            <p class="mb-0 text-muted">&copy; <?php echo e(date('Y')); ?> <?php echo e(config('app.name')); ?>. All rights reserved.</p>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <p class="mb-0 text-muted">Version 1.0.0</p>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    </div>

    <!-- Core JS -->
    <script src="<?php echo e(asset('js/jQuery-3.5.1.min.js')); ?>"></script>
    <script src="<?php echo e(asset('js/popper.min.js')); ?>"></script>
    <script src="<?php echo e(asset('js/bootstrip5.3.3.min.js')); ?>"></script>
    <script src="<?php echo e(asset('js/jquery.dataTables.min.js')); ?>"></script>

    <!-- Third-party JS -->
    <script src="<?php echo e(asset('js/sweatalert2.all.min.js')); ?>"></script>
    <script src="<?php echo e(asset('js/multi-select-tags.js')); ?>"></script>

    <!-- Theme JS -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/js/theme.js', 'resources/js/utils.js']); ?>

    <!-- Loading functionality -->
    <script>
        function showLoading(isDataTable = false) {
            $('#loadingModal').modal('show');

            if (isDataTable) {
                $("#loadingSvg").addClass('d-none');
                $(".loader").removeClass('d-none');
            } else {
                $("#loadingSvg").removeClass('d-none');
                $(".loader").addClass('d-none');
            }
        }

        function hideLoading() {
            $('#loadingModal').modal('hide');
        }
    </script>

    <!-- Additional JS -->
    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>

</html>
<?php /**PATH E:\Web_Development\testerReport\resources\views/layouts/app.blade.php ENDPATH**/ ?>