1748117362O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:5:{i:0;O:18:"App\Models\Project":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"projects";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:1;s:4:"name";s:8:"Un roads";s:11:"description";N;s:10:"created_at";s:19:"2024-09-04 18:37:36";s:10:"updated_at";s:19:"2024-09-04 18:37:36";}s:11:" * original";a:5:{s:2:"id";i:1;s:4:"name";s:8:"Un roads";s:11:"description";N;s:10:"created_at";s:19:"2024-09-04 18:37:36";s:10:"updated_at";s:19:"2024-09-04 18:37:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:2:{i:0;s:4:"name";i:1;s:11:"description";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:1;O:18:"App\Models\Project":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"projects";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:2;s:4:"name";s:14:"Polio Vaccines";s:11:"description";N;s:10:"created_at";s:19:"2024-09-04 18:37:36";s:10:"updated_at";s:19:"2024-09-04 18:37:36";}s:11:" * original";a:5:{s:2:"id";i:2;s:4:"name";s:14:"Polio Vaccines";s:11:"description";N;s:10:"created_at";s:19:"2024-09-04 18:37:36";s:10:"updated_at";s:19:"2024-09-04 18:37:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:2:{i:0;s:4:"name";i:1;s:11:"description";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:2;O:18:"App\Models\Project":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"projects";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:3;s:4:"name";s:16:"Inj distribution";s:11:"description";N;s:10:"created_at";s:19:"2024-09-04 18:37:36";s:10:"updated_at";s:19:"2024-09-04 18:37:36";}s:11:" * original";a:5:{s:2:"id";i:3;s:4:"name";s:16:"Inj distribution";s:11:"description";N;s:10:"created_at";s:19:"2024-09-04 18:37:36";s:10:"updated_at";s:19:"2024-09-04 18:37:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:2:{i:0;s:4:"name";i:1;s:11:"description";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:3;O:18:"App\Models\Project":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"projects";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:4;s:4:"name";s:7:"booster";s:11:"description";N;s:10:"created_at";s:19:"2024-09-10 18:48:14";s:10:"updated_at";s:19:"2024-09-10 18:48:14";}s:11:" * original";a:5:{s:2:"id";i:4;s:4:"name";s:7:"booster";s:11:"description";N;s:10:"created_at";s:19:"2024-09-10 18:48:14";s:10:"updated_at";s:19:"2024-09-10 18:48:14";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:2:{i:0;s:4:"name";i:1;s:11:"description";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:4;O:18:"App\Models\Project":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"projects";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:5;s:4:"name";s:10:"sesde dese";s:11:"description";N;s:10:"created_at";s:19:"2025-05-23 06:26:14";s:10:"updated_at";s:19:"2025-05-23 06:26:14";}s:11:" * original";a:5:{s:2:"id";i:5;s:4:"name";s:10:"sesde dese";s:11:"description";N;s:10:"created_at";s:19:"2025-05-23 06:26:14";s:10:"updated_at";s:19:"2025-05-23 06:26:14";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:2:{i:0;s:4:"name";i:1;s:11:"description";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}