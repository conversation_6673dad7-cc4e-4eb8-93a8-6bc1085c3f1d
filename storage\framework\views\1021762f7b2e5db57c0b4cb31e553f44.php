<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AppLayout::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="py-12">
        <div class="px-4">
            <div class="bg-white ">
                <div class="outer d-flex justify-between align-items-center px-4 border-bottom">
                    <div class="p-6 text-gray-900 d-flex justify-center w-75 font-semibold text-2xl">
                        <?php echo e(__("All QA's Report")); ?>

                    </div>
                    <div>
                        <button type="button" class="btn btn-primary"  data-bs-toggle="modal" data-bs-target="#modal-center">
                            Add Project
                        </button>
                    </div>
                </div>
                <div class="py-2 px-2">
                    <table id="project-table" class="table table-striped table-bordered" style="width:100%">
                        <thead>
                            <tr>
                                <th> Name</th>
                                <th> Description</th>
                            </tr>
                        </thead>
                        <tbody>

                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <!-- Modal -->
    <?php echo $__env->make('modals.add-project', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<script>
    $(document).ready(function() {
        loadProject();
        $('#projectForm').on('submit', function (e) {
            console.log("Form is submitted ");
            showLoading();
            e.preventDefault(); // Prevent the default form submission
            let formData = new FormData(this);

            fetch("<?php echo e(route('project.store')); ?>", {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                }
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        hideLoading();
                        $('#close-modal').click();
                        $('#projectForm')[0].reset();
                        loadProject();
                    } else {
                        // Handle validation errors
                        console.log(data.errors);
                    }
                })
                .catch(error => console.error('Error: ', error));
        });
        function loadProject(){
            if($.fn.dataTable.isDataTable('#project-table')){
                $('#project-table').DataTable().clear().destroy();
            }
            $('#project-table').DataTable({
                // processing: true,
                serverSide: true,
                dom: '<"top"f> rt<"bottom"ip><"clear">',
                ajax: {
                   url: '<?php echo e(route("project.data")); ?>',
                   beforeSend :function(){
                    showLoading(true);
                   },
                   complete : function(){
                    hideLoading();
                   }
                
                },
                columns: [
                    { data: 'name', name: 'name' },
                    { data: 'description', name: 'description' }
                ]
            });
        }
    });
</script>
<?php /**PATH E:\Web_Development\testerReport\resources\views/qareport/project.blade.php ENDPATH**/ ?>